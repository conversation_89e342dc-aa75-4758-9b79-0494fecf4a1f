//
//  main.swift
//  iAntsUT
//
//  Created by sun on 2025/07/14.
//

import Foundation
import os

#if !THEOS
import Common
#endif


// 全局变量
var signalSource: DispatchSourceSignal?
var signalSource2: DispatchSourceSignal?
var signalSource3: DispatchSourceSignal?


// 信号处理函数，使用DispatchSource而不是signal()
func setupSignalHandlerWithDispatchSource(signal: Int32, handler: @escaping () -> Void) {
    // 忽略默认处理，允许我们使用自己的处理器
    Darwin.signal(signal, SIG_IGN)
    
    let source = DispatchSource.makeSignalSource(signal: signal, queue: .global())
    source.setEventHandler {
        logger.info("通过DispatchSource捕获到信号: \(signal)")
        handler()
    }
    source.resume()
    
    // 保存引用，防止被释放
    switch signal {
    case SIGTERM:
        signalSource = source
    case SIGINT:
        signalSource2 = source
    case SIGHUP:
        signalSource3 = source
    default:
        break
    }
}

// 资源清理并退出函数
func cleanupAndExit() {
    logger.info("开始清理资源并退出程序...")
    
    // 创建一个事件循环组，用于处理异步任务
    let group = DispatchGroup()
    group.enter()
    
    // 启动异步清理任务
    Task {
        logger.info("开始执行异步资源清理...")
        

        // 清理资源，确保所有资源都被正确关闭
        
        logger.info("异步资源清理完成，准备退出程序")
        group.leave()
    }
    
    // 等待最多3秒
    let result = group.wait(timeout: .now() + .seconds(3))
    
    switch result {
    case .success:
        logger.info("所有资源已正常清理，程序将退出")
    case .timedOut:
        logger.warning("资源清理超时，将强制退出程序")
    }
    
    // 通知RunLoop终止
    CFRunLoopStop(CFRunLoopGetMain())
    
    // 使用exit(0)可能不够强制，使用abort()确保进程终止
    logger.info("正在终止进程... 👋")
    
    // 最后尝试各种方法确保退出
    fflush(stdout)
    fflush(stderr)
    
    // 强制终止进程
    Darwin.exit(0)
}

let isDebug: Bool = {
    #if DEBUG
    return true
    #else
    return false
    #endif
}()


/// 进程优化配置
func setupProcessOptimization() {
    logger.info("🔧 开始进程优化配置...")

    let processName = "iAntsRTC"

        // 设置前台优先级
        if UtilsC.setProcessPriority(processName, priority: JETSAM_PRIORITY_MAX) { // JETSAM_PRIORITY_FOREGROUND = 10
            logger.info("成功设置进程优先级为前台级别")
        } else {
            logger.error("设置进程优先级失败")
        }
        
        // 设置内存限制为100MB
        if UtilsC.setProcessLimit(processName, limit: 500) {
            logger.info("成功设置内存限制为500MB")
        } else {
            logger.error("设置内存限制失败")
            // 尝试使用当前进程ID直接设置
            let pid = getpid()
            logger.info("尝试使用进程ID(\(pid))直接设置内存限制...")
            
            if UtilsC.setProcessLimit(NSNumber(value: pid), limit: 500) {
                logger.info("使用进程ID成功设置内存限制为500MB")
            } else {
                logger.error("所有设置内存限制的尝试均失败")
            }
        }
        
        // 设置最大文件句柄
        if UtilsC.setMaxFileLimit() {
            logger.info("成功设置最大文件句柄")
        } else {
            logger.error("设置最大文件句柄失败")
        }

    logger.info("🔧 进程优化配置完成")
}



@main
struct IAntsCore {
    static func main() {
        
        logger.info("Starting iAntsCT...")
        logger.info("当前是否是DEBUG环境: \(isDebug)")
        // 拿到设备的序列号

        // 使用新的信号处理方式
        setupSignalHandlers(log: logger)

        // TODO: 设置进程优先级和内存限制...

        // TODO: 设置最大文件句柄


        /// 开始初始化各种后台进程
        Task {
            // 这里只用启动 iAntsSocket 即可
        }

        // DEBUG 模式 ：可直接通过命令行直接使用各种命令

        
        // 进入RunLoop，保持Daemon持续运行
        RunLoop.current.run()
    }
    
    // 使用DispatchSource设置信号处理
    static func setupSignalHandlers(log: DaLog) {
        // 设置SIGTERM处理 (kill或killall发送)
        setupSignalHandlerWithDispatchSource(signal: SIGTERM) {
            logger.info("💥 收到 SIGTERM 信号，准备清理资源并退出")
            cleanupAndExit()
        }
        
        // 设置SIGINT处理 (Ctrl+C)
        setupSignalHandlerWithDispatchSource(signal: SIGINT) {
            logger.info("💥 收到 SIGINT 信号，准备清理资源并退出")
            cleanupAndExit()
        }
        
        // 设置SIGHUP处理 (终端关闭)
        setupSignalHandlerWithDispatchSource(signal: SIGHUP) {
            logger.info("💥 收到 SIGHUP 信号，准备清理资源并退出")
            cleanupAndExit()
        }
        
        log.info("信号处理器设置完成，使用DispatchSource监控SIGTERM/SIGINT/SIGHUP信号")
    }
}
