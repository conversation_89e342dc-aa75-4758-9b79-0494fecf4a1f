import Foundation

// MARK: - WebSocket Client Delegate Protocol

public protocol WSClientDelegate: AnyObject {
    func wsClientDidConnect(_ client: WSClient)
    func wsClientDidDisconnect(_ client: WSClient, error: Error?)
    func wsClient(_ client: WSClient, didReceiveData data: Data)
    func wsClient(_ client: WSClient, didReceiveError error: Error)
}

// MARK: - Simple WebSocket Client using Delegate Pattern

public class WSClient: NSObject, URLSessionWebSocketDelegate {

    // MARK: - Types

    public enum State: Equatable {
        case disconnected
        case connecting
        case connected
        case error(String)
    }

    public enum ClientError: Error, LocalizedError {
        case connectionFailed
        case notConnected
        case sendFailed

        public var errorDescription: String? {
            switch self {
            case .connectionFailed: return "connectionFailed"
            case .notConnected: return "Not connected"
            case .sendFailed: return "Send failed"
            }
        }
    }

    // MARK: - Properties

    private let serverURL: URL
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession!
    private var isManualDisconnect = false

    // State
    private var _state: State = .disconnected
    public var state: State { _state }

    // Delegate
    public weak var delegate: WSClientDelegate?

    // MARK: - Initialization

    public init(serverURL: URL) {
        self.serverURL = serverURL

        // 配置 URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 0  // 不设置请求超时
        config.timeoutIntervalForResource = 0  // 不设置资源超时
        config.waitsForConnectivity = false

        // 先调用 super.init()
        super.init()

        // 然后设置 URLSession
        self.urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }

    deinit {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
    }

    // MARK: - Public Interface

    public func connect() async throws {
        guard _state == .disconnected else {
            print("WSClient: Already connected or connecting")
            return
        }

        isManualDisconnect = false
        setState(.connecting)

        print("WSClient: Connecting to \(serverURL)")

        // 创建 WebSocket 连接
        let request = URLRequest(url: serverURL)
        webSocketTask = urlSession.webSocketTask(with: request)
        webSocketTask?.resume()

        // 等待连接建立
        try await waitForConnection()

        // 开始接收消息
        startReceiving()
    }

    /// 等待连接建立
    private func waitForConnection() async throws {
        // 使用简单的轮询方式检查连接状态
        for _ in 0..<50 { // 最多等待5秒 (50 * 100ms)
            if _state == .connected {
                return
            }
            if case .error(_) = _state {
                throw ClientError.connectionFailed
            }
            try await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }

        // 超时
        setState(.error("Connection timeout"))
        throw ClientError.connectionFailed
    }
    
    public func disconnect() {
        isManualDisconnect = true
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        setState(.disconnected)
        print("WSClient: Disconnected")
    }

    public func sendProtobufData(_ data: Data) async throws {
        guard _state == .connected, let webSocketTask = webSocketTask else {
            throw ClientError.notConnected
        }

        let message = URLSessionWebSocketTask.Message.data(data)
        do {
            try await webSocketTask.send(message)
        } catch {
            throw ClientError.sendFailed
        }
    }

    // MARK: - Private Methods

    private func setState(_ newState: State) {
        _state = newState

        // 通知委托状态变化
        switch newState {
        case .connected:
            delegate?.wsClientDidConnect(self)
        case .disconnected:
            delegate?.wsClientDidDisconnect(self, error: nil)
        case .error(_):
            let error = ClientError.connectionFailed
            delegate?.wsClientDidDisconnect(self, error: error)
        default:
            break
        }
    }

    private func startReceiving() {
        // 使用独立的后台队列来处理消息接收，避免阻塞主线程
        Task.detached { [weak self] in
            await self?.receiveLoop()
        }
    }

    private func receiveLoop() async {
        print("WSClient: Starting receive loop")

        while !isManualDisconnect && _state == .connected {
            do {
                guard let webSocketTask = webSocketTask else {
                    print("WSClient: WebSocket task is nil, breaking receive loop")
                    break
                }

                print("WSClient: Waiting for message...")
                let message = try await webSocketTask.receive()
                print("WSClient: Received message")

                await handleReceivedMessage(message)

                // 给其他任务一些执行时间
                await Task.yield()

            } catch {
                print("WSClient: Receive error: \(error)")
                handleConnectionError(error)
                break
            }
        }

        print("WSClient: Receive loop ended")
    }

    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            delegate?.wsClient(self, didReceiveData: data)
        case .string(let text):
            if let data = text.data(using: .utf8) {
                delegate?.wsClient(self, didReceiveData: data)
            }
        @unknown default:
            print("WSClient: Received unknown message type")
        }
    }

    private func handleConnectionError(_ error: Error) {
        setState(.error(error.localizedDescription))
        delegate?.wsClient(self, didReceiveError: error)
    }

    // MARK: - URLSessionWebSocketDelegate

    public func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        print("WSClient: WebSocket connected")
        setState(.connected)
    }

    public func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        print("WSClient: WebSocket disconnected with code: \(closeCode)")
        if !isManualDisconnect {
            setState(.disconnected)
        }
    }

    public func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            print("WSClient: WebSocket task completed with error: \(error)")
            handleConnectionError(error)
        } else {
            print("WSClient: WebSocket task completed successfully")
        }
    }
}
