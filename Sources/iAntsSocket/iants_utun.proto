syntax = "proto3";

package iAnts;

import "iants.proto";

// UT 专用消息类型
enum UTMessageType {
  UT_STATUS = 0;
  UT_GET_CONFIG = 1;
  UT_ROUTER_CONFIG = 2;
  UT_ROUTER_ACTION = 3;
  UT_IP_CONFIG = 4;
  UT_PROXY_CONFIG = 5;
  UT_PROXY_ACTION = 6;
  UT_PROXY_STATUS = 7;
  UT_DEFAULT = 99;
}

// UT 专用消息体
message UT_StatusMessage {
  bool utun_created = 1;
  string interface_name = 2;
  int32 unit = 3;
  string ip_address = 4;
  string dest_address = 5;
  int32 mtu = 6;
  bool interface_up = 7;
  bool proxy_running = 8;
  string proxy_url = 9;
  repeated string routes = 10;
}

message UT_GetConfigMessage {
  // 空消息
}

message UT_RouterConfigMessage {
  string strategy = 1;
  repeated string whitelist = 2;
  repeated string blacklist = 3;
  bool enable_dns_hijack = 4;
  string dns_server = 5;
}

message UT_RouterActionMessage {
  enum Action {
    ADD = 0;
    DELETE = 1;
    MODIFY = 2;
    RESTORE_DEFAULT = 3;
  }
  Action action = 1;
  string destination = 2;
  string gateway = 3;
  string netmask = 4;
  string interface = 5;
  int32 metric = 6;
}

message UT_IPConfigMessage {
  string ip_address = 1;
  string dest_address = 2;
  string netmask = 3;
  int32 mtu = 4;
  bool bring_up = 5;
}

message UT_ProxyConfigMessage {
  enum ProxyType {
    HTTP = 0;
    HTTPS = 1;
    SOCKS5 = 2;
    SHADOWSOCKS = 3;
  }
  ProxyType type = 1;
  string host = 2;
  int32 port = 3;
  string username = 4;
  string password = 5;
  string extra_config = 6;
}

message UT_ProxyActionMessage {
  enum Action {
    START = 0;
    STOP = 1;
    RESTART = 2;
  }
  Action action = 1;
}

message UT_ProxyStatusMessage {
  bool running = 1;
  string proxy_url = 2;
  int64 bytes_sent = 3;
  int64 bytes_received = 4;
  int64 start_time = 5;
}

message UT_DefaultMessage {
  // 空消息
}

// UT 完整消息
message UTMessage {
  oneof message_type {
    CommonMessageType common_type = 1;
    UTMessageType ut_type = 2;
  }

  oneof body {
    // 公共消息
    Heartbeat heartbeat = 10;
    ErrorMessage error = 11;
    
    // UT 专用消息
    UT_StatusMessage ut_status = 20;
    UT_GetConfigMessage ut_get_config = 21;
    UT_RouterConfigMessage ut_router_config = 22;
    UT_RouterActionMessage ut_router_action = 23;
    UT_IPConfigMessage ut_ip_config = 24;
    UT_ProxyConfigMessage ut_proxy_config = 25;
    UT_ProxyActionMessage ut_proxy_action = 26;
    UT_ProxyStatusMessage ut_proxy_status = 27;
    UT_DefaultMessage ut_default = 28;
  }
}
