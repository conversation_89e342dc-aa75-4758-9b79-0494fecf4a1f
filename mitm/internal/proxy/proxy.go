package proxy

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"iantsMitm/internal/cert"
	"iantsMitm/internal/config"
	"iantsMitm/internal/interceptor"
	"iantsMitm/internal/plugin"
	"iantsMitm/internal/socks5"
	"iantsMitm/internal/storage"

	"github.com/elazarl/goproxy"
)

// Server 代理服务器
type Server struct {
	config        *config.Config
	proxy         *goproxy.ProxyHttpServer
	interceptor   *interceptor.Interceptor
	storage       *storage.Storage
	certManager   *cert.CertManager
	socks5Server  *socks5.Server
	pluginManager *plugin.Manager
}

// NewServer 创建新的代理服务器
func NewServer(cfg *config.Config) *Server {
	server := &Server{
		config: cfg,
		proxy:  goproxy.NewProxyHttpServer(),
	}

	// 创建证书管理器
	server.certManager = cert.NewCertManager(cfg.CertPath, cfg.KeyPath)

	// 创建拦截器
	server.interceptor = interceptor.NewInterceptor(cfg)

	// 创建存储管理器
	server.storage = storage.NewStorage(cfg)

	// 创建SOCKS5服务器
	server.socks5Server = socks5.NewServer(cfg)

	// 创建插件管理器
	pluginContext := &plugin.PluginContext{
		Config:   make(map[string]interface{}),
		DataPath: cfg.DataSavePath,
		LogLevel: cfg.LogLevel,
		UserData: make(map[string]interface{}),
	}
	server.pluginManager = plugin.NewManager(pluginContext)

	// 注册示例插件
	server.registerDefaultPlugins()

	// 配置代理服务器
	server.setupProxy()

	return server
}

// setupProxy 配置代理服务器
func (s *Server) setupProxy() {
	// 设置详细日志
	s.proxy.Verbose = s.config.Verbose

	// 配置传输层
	s.proxy.Tr = &http.Transport{
		MaxIdleConns:        s.config.MaxConcurrent,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		TLSHandshakeTimeout: 10 * time.Second,
	}

	// 初始化证书管理器并启用HTTPS MITM
	if s.config.EnableHTTPS {
		if s.config.AutoGenCert {
			if err := s.certManager.Initialize(); err != nil {
				log.Printf("❌ 证书初始化失败: %v", err)
			}
		}
		s.proxy.OnRequest().HandleConnect(goproxy.AlwaysMitm)
		log.Println("🔐 HTTPS MITM已启用")
	}

	// 设置请求拦截（集成插件系统）
	s.proxy.OnRequest().DoFunc(s.handleRequestWithPlugins)

	// 设置响应拦截（集成插件系统）
	s.proxy.OnResponse().DoFunc(s.handleResponseWithPlugins)

	// 设置WebSocket连接处理
	s.proxy.OnRequest().HandleConnectFunc(s.interceptor.HandleWebSocketConnect)

	log.Println("✅ 代理服务器配置完成")
}

// Start 启动代理服务器
func (s *Server) Start() error {
	// 验证配置
	if err := s.config.Validate(); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 启动存储服务
	s.storage.Start(s.interceptor.GetDataChannel())

	// 启动SOCKS5服务器
	if err := s.socks5Server.Start(); err != nil {
		log.Printf("⚠️ SOCKS5服务器启动失败: %v", err)
	}

	// 打印启动信息
	s.printStartupInfo()

	// 启动HTTP代理服务器
	log.Printf("🚀 代理服务器启动在端口 %s", s.config.Port)
	return http.ListenAndServe(":"+s.config.Port, s.proxy)
}

// Stop 停止代理服务器
func (s *Server) Stop() {
	log.Println("🛑 正在停止代理服务器...")

	// 停止SOCKS5服务器
	s.socks5Server.Stop()

	// 清理插件系统
	s.pluginManager.Cleanup()

	// 停止存储服务
	s.storage.Stop()

	log.Println("✅ 代理服务器已停止")
}

// printStartupInfo 打印启动信息
func (s *Server) printStartupInfo() {
	separator := strings.Repeat("=", 50)
	fmt.Println(separator)
	fmt.Println("🚀 iAnts MITM 代理服务器")
	fmt.Println(separator)
	fmt.Printf("📡 监听端口: %s\n", s.config.Port)

	protocols := []string{"HTTP"}
	if s.config.EnableHTTPS {
		protocols = append(protocols, "HTTPS", "WebSocket", "WSS")
	}
	if s.config.EnableSOCKS5 {
		protocols = append(protocols, "SOCKS5")
	}
	fmt.Printf("📋 支持协议: %v\n", protocols)

	if s.config.EnableHTTPS {
		fmt.Println("🔐 HTTPS 自动解密: 已启用")
		certPath, _ := s.certManager.GetCertificateInfo()
		if certPath != "" {
			fmt.Printf("📖 CA证书路径: %s\n", certPath)
		}
	}

	if s.config.EnableLocalSave {
		fmt.Printf("💾 本地存储: 已启用 (%s)\n", s.config.DataSavePath)
	}

	if s.config.EnableUpload {
		fmt.Printf("📤 远程上传: 已启用 (%s)\n", s.config.UploadURL)
	}

	fmt.Printf("⚡ 最大并发: %d\n", s.config.MaxConcurrent)
	fmt.Printf("📊 缓冲区大小: %d\n", s.config.MaxBufferSize)
	fmt.Printf("⏱️ 保存间隔: %d秒\n", s.config.SaveInterval)

	if len(s.config.BlockedHosts) > 0 {
		fmt.Printf("🚫 阻止主机: %v\n", s.config.BlockedHosts)
	}

	fmt.Println(separator)
	fmt.Println("✅ 服务器准备就绪，等待连接...")
	fmt.Println(separator)
}

// registerDefaultPlugins 注册默认插件
func (s *Server) registerDefaultPlugins() {
	// 注册示例请求过滤器
	if err := s.pluginManager.RegisterPlugin(plugin.NewExampleRequestFilter()); err != nil {
		log.Printf("⚠️ 注册示例请求过滤器失败: %v", err)
	}

	// 注册示例数据处理器
	if err := s.pluginManager.RegisterPlugin(plugin.NewExampleDataProcessor()); err != nil {
		log.Printf("⚠️ 注册示例数据处理器失败: %v", err)
	}

	// 注册示例自定义插件
	if err := s.pluginManager.RegisterPlugin(plugin.NewExampleCustomPlugin()); err != nil {
		log.Printf("⚠️ 注册示例自定义插件失败: %v", err)
	}
}

// handleRequestWithPlugins 处理请求（集成插件系统）
func (s *Server) handleRequestWithPlugins(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
	// 先通过插件系统处理请求
	modifiedReq, pluginResp := s.pluginManager.ProcessRequest(req, ctx)
	if pluginResp != nil {
		return modifiedReq, pluginResp
	}

	// 然后通过拦截器处理
	return s.interceptor.HandleHTTPRequest(modifiedReq, ctx)
}

// handleResponseWithPlugins 处理响应（集成插件系统）
func (s *Server) handleResponseWithPlugins(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
	// 先通过拦截器处理
	interceptedResp := s.interceptor.HandleHTTPResponse(resp, ctx)

	// 然后通过插件系统处理
	return s.pluginManager.ProcessResponse(interceptedResp, ctx)
}

// GetPluginManager 获取插件管理器（用于外部访问）
func (s *Server) GetPluginManager() *plugin.Manager {
	return s.pluginManager
}
