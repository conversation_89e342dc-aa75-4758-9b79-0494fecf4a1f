package interceptor

import (
	"bytes"
	"crypto/rand"
	"fmt"
	"io"
	"log"
	"math/big"
	"net/http"
	"regexp"
	"strings"
	"sync/atomic"
	"time"

	"iantsMitm/internal/config"

	"github.com/elazarl/goproxy"
)

var idCounter int64

// InterceptedData 拦截的数据包结构
type InterceptedData struct {
	ID        string            `json:"id"`
	Timestamp time.Time         `json:"timestamp"`
	Method    string            `json:"method"`
	URL       string            `json:"url"`
	Headers   map[string]string `json:"headers"`
	Body      string            `json:"body,omitempty"`
	Response  *ResponseData     `json:"response,omitempty"`
	Protocol  string            `json:"protocol"` // HTTP/HTTPS/WebSocket/WSS
	Size      int64             `json:"size"`
	Duration  time.Duration     `json:"duration,omitempty"`
}

// ResponseData 响应数据结构
type ResponseData struct {
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body,omitempty"`
	Size       int64             `json:"size"`
}

// Interceptor 流量拦截器
type Interceptor struct {
	config       *config.Config
	dataChannel  chan InterceptedData
	blockedHosts []*regexp.Regexp
}

// NewInterceptor 创建新的拦截器
func NewInterceptor(cfg *config.Config) *Interceptor {
	interceptor := &Interceptor{
		config:      cfg,
		dataChannel: make(chan InterceptedData, cfg.MaxBufferSize),
	}

	// 编译阻止主机的正则表达式
	for _, host := range cfg.BlockedHosts {
		if regex, err := regexp.Compile(host); err == nil {
			interceptor.blockedHosts = append(interceptor.blockedHosts, regex)
		}
	}

	return interceptor
}

// GetDataChannel 获取数据通道
func (i *Interceptor) GetDataChannel() <-chan InterceptedData {
	return i.dataChannel
}

// HandleHTTPRequest 处理HTTP请求
func (i *Interceptor) HandleHTTPRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
	// 检查是否被阻止
	if i.isBlocked(req.URL.Host) {
		log.Printf("🚫 阻止访问: %s", req.URL.Host)
		return req, goproxy.NewResponse(req, goproxy.ContentTypeText,
			http.StatusForbidden, "🚫 访问被阻止")
	}

	// 检查请求体大小限制
	if req.ContentLength > i.config.MaxBodySize {
		log.Printf("⚠️ 请求体过大: %d bytes", req.ContentLength)
		return req, goproxy.NewResponse(req, goproxy.ContentTypeText,
			http.StatusRequestEntityTooLarge, "请求体过大")
	}

	startTime := time.Now()

	// 创建拦截数据
	interceptedData := InterceptedData{
		ID:        generateID(),
		Timestamp: startTime,
		Method:    req.Method,
		URL:       req.URL.String(),
		Headers:   i.flattenHeaders(req.Header),
		Protocol:  i.getProtocol(req),
		Size:      req.ContentLength,
	}

	// 读取请求体（如果有）
	if req.Body != nil && req.ContentLength > 0 {
		body, err := io.ReadAll(req.Body)
		if err == nil {
			interceptedData.Body = string(body)
			// 重新创建请求体供后续使用
			req.Body = io.NopCloser(bytes.NewReader(body))
		}
	}

	// 将开始时间存储在上下文中，用于计算持续时间
	ctx.UserData = startTime

	// 发送到数据通道
	select {
	case i.dataChannel <- interceptedData:
	default:
		log.Printf("⚠️ 数据通道已满，丢弃数据: %s", req.URL.String())
	}

	if i.config.Verbose {
		log.Printf("📨 拦截请求: %s %s", req.Method, req.URL.String())
	}

	return req, nil
}

// HandleHTTPResponse 处理HTTP响应
func (i *Interceptor) HandleHTTPResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
	if resp == nil {
		return resp
	}

	// 获取开始时间
	var duration time.Duration
	if startTime, ok := ctx.UserData.(time.Time); ok {
		duration = time.Since(startTime)
	}

	// 创建响应数据
	responseData := &ResponseData{
		StatusCode: resp.StatusCode,
		Headers:    i.flattenHeaders(resp.Header),
		Size:       resp.ContentLength,
	}

	// 读取响应体
	if resp.Body != nil {
		body, err := io.ReadAll(resp.Body)
		if err == nil && int64(len(body)) <= i.config.MaxBodySize {
			responseData.Body = string(body)
			responseData.Size = int64(len(body))
			// 重新创建响应体
			resp.Body = io.NopCloser(bytes.NewReader(body))
		} else if err == nil {
			// 响应体太大，只记录大小
			responseData.Size = int64(len(body))
			resp.Body = io.NopCloser(bytes.NewReader(body))
		}
	}

	// 创建完整的拦截数据
	interceptedData := InterceptedData{
		ID:        generateID(),
		Timestamp: time.Now(),
		Method:    ctx.Req.Method,
		URL:       ctx.Req.URL.String(),
		Headers:   i.flattenHeaders(ctx.Req.Header),
		Protocol:  i.getProtocol(ctx.Req),
		Response:  responseData,
		Duration:  duration,
	}

	// 发送到数据通道
	select {
	case i.dataChannel <- interceptedData:
	default:
		log.Printf("⚠️ 数据通道已满，丢弃响应数据: %s", ctx.Req.URL.String())
	}

	if i.config.Verbose {
		log.Printf("📬 拦截响应: %d %s (耗时: %v)", resp.StatusCode, ctx.Req.URL.String(), duration)
	}

	return resp
}

// HandleWebSocketConnect 处理WebSocket连接
func (i *Interceptor) HandleWebSocketConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string) {
	// 检查是否是 WebSocket 升级请求
	if i.isWebSocketRequest(ctx.Req) {
		log.Printf("🔌 检测到 WebSocket 连接: %s", host)

		// 记录WebSocket连接
		interceptedData := InterceptedData{
			ID:        generateID(),
			Timestamp: time.Now(),
			Method:    "CONNECT",
			URL:       "ws://" + host,
			Headers:   i.flattenHeaders(ctx.Req.Header),
			Protocol:  i.getWebSocketProtocol(ctx.Req),
		}

		select {
		case i.dataChannel <- interceptedData:
		default:
			log.Printf("⚠️ 数据通道已满，丢弃WebSocket连接数据: %s", host)
		}

		return goproxy.MitmConnect, host
	}

	return goproxy.MitmConnect, host
}

// 辅助方法

// generateID 生成唯一ID
func generateID() string {
	// 使用原子计数器 + 时间戳 + 加密随机数确保唯一性
	counter := atomic.AddInt64(&idCounter, 1)
	timestamp := time.Now().UnixNano()
	randomPart := secureRandomString(8)

	return fmt.Sprintf("%d-%d-%s", timestamp, counter, randomPart)
}

// secureRandomString 生成加密安全的随机字符串
func secureRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)

	for i := range b {
		// 使用加密安全的随机数生成器
		if num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset)))); err == nil {
			b[i] = charset[num.Int64()]
		} else {
			// 降级到时间戳随机（不应该发生）
			b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		}
	}
	return string(b)
}

// isBlocked 检查主机是否被阻止
func (i *Interceptor) isBlocked(host string) bool {
	for _, regex := range i.blockedHosts {
		if regex.MatchString(host) {
			return true
		}
	}
	return false
}

// flattenHeaders 扁平化HTTP头
func (i *Interceptor) flattenHeaders(headers http.Header) map[string]string {
	flat := make(map[string]string)
	for key, values := range headers {
		flat[key] = strings.Join(values, "; ")
	}
	return flat
}

// getProtocol 获取协议类型
func (i *Interceptor) getProtocol(req *http.Request) string {
	if req.TLS != nil {
		return "HTTPS"
	}
	if i.isWebSocketRequest(req) {
		if req.TLS != nil {
			return "WSS"
		}
		return "WebSocket"
	}
	return "HTTP"
}

// getWebSocketProtocol 获取WebSocket协议类型
func (i *Interceptor) getWebSocketProtocol(req *http.Request) string {
	if req.TLS != nil {
		return "WSS"
	}
	return "WebSocket"
}

// isWebSocketRequest 检查是否是WebSocket请求
func (i *Interceptor) isWebSocketRequest(req *http.Request) bool {
	return strings.ToLower(req.Header.Get("Upgrade")) == "websocket"
}
